/**
 * 语言设置相关API
 * <AUTHOR> Assistant
 */

import { moduleIpcRequest } from "@/api/request";
const ipc = moduleIpcRequest("controller/common/language/");

// 语言设置接口
export interface LanguageRequest {
  language: string;
}

export interface LanguageResponse {
  language: string;
}

/**
 * 语言API
 */
const languageApi = {
  /**
   * 设置语言
   * @param params 语言参数
   * @returns 设置结果
   */
  setLanguage(params: LanguageRequest) {
    return ipc.invoke<LanguageResponse>("setLanguage", params);
  },

  /**
   * 获取当前语言
   * @returns 当前语言
   */
  getCurrentLanguage() {
    return ipc.invoke<LanguageResponse>("getCurrentLanguage");
  },

  /**
   * 同步语言设置到后端
   * @param params 语言参数
   * @returns 同步结果
   */
  syncLanguage(params: LanguageRequest) {
    return ipc.invoke<LanguageResponse>("syncLanguage", params);
  }
};

export { languageApi };
