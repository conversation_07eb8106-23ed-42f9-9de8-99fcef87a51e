<template>
  <el-config-provider :locale="locale" :size="assemblySize" :button="buttonConfig">
    <div v-if="isChecking || isRouteLoading" class="loading-container">
      <div class="loading-content">
        <div class="loading-logo-wrapper">
          <div class="circular-loading">
            <svg class="circular-bg" viewBox="0 0 50 50">
              <circle class="circular-track" cx="25" cy="25" r="22" fill="none" stroke-width="5" />
              <circle class="circular-bar" cx="25" cy="25" r="22" fill="none" stroke-width="5" />
            </svg>
            <span class="logo-svg">
              <img src="@/assets/svg/logo.svg" width="32" height="28" alt="logo" />
            </span>
          </div>
        </div>
        <div class="loading-text">{{ isChecking ? t("app.loading.checking") : t("app.loading.loading") }}</div>
        <div class="loading-progress">
          <div class="progress-bar">
            <div class="progress-inner"></div>
          </div>
        </div>
      </div>
    </div>
    <router-view v-else v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </transition>
    </router-view>
  </el-config-provider>
</template>

<script setup lang="ts">
import { reactive, computed, onBeforeMount, onMounted, ref } from "vue";
import { useTheme } from "@/hooks/useTheme";
import { ElConfigProvider } from "element-plus";
import { useGlobalStore } from "@/stores/modules/global";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import es from "element-plus/es/locale/lang/es";
import fr from "element-plus/es/locale/lang/fr";
import hotkeys from "hotkeys-js";
import { windowControlApi } from "@/api";
import { licenseApi } from "./api/modules/sys/activate";
import { useActivateStore } from "./stores/modules";
import { useRouter } from "vue-router";
import { ACTIVATE_URL, HOME_URL } from "./config";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { LanguageType } from "./stores/interface";
import { getBrowserLang } from "@/utils";
import { languageApi } from "@/api/modules/sys/language";
import { routeStateManager } from "@/utils/routeStateManager";

const router = useRouter();
const activateStore = useActivateStore();
const globalStore = useGlobalStore();
const isChecking = ref(true);
const isRouteLoading = ref(false);
const { t } = useI18n();

// init language
const i18n = useI18n();

const bindKeys = (): void => {
  hotkeys("f12", () => {
    openDevTools();
  });
};

const unbindKeys = (): void => {
  hotkeys.unbind("f12");
};

const openDevTools = (): void => {
  windowControlApi.openDevTools();
};

const checkAuth = async () => {
  console.log(t("app.loading.checking"));
  try {
    const result = await licenseApi.checkAuth();
    // console.log("授权检查结果:", result);
    if (result.code == 0) {
      activateStore.setActivated(true);
      isRouteLoading.value = true;

      // 检查是否有保存的路由状态
      const savedState = routeStateManager.getSavedState();
      if (savedState && savedState.currentPath && savedState.currentPath !== '/' && savedState.currentPath !== HOME_URL) {
        console.log("发现保存的路由状态，准备恢复:", savedState.currentPath);
        // 不立即跳转到HOME_URL，让路由状态管理器处理恢复
        await new Promise(resolve => setTimeout(resolve, 100));
      } else {
        await Promise.all([router.replace(HOME_URL), new Promise(resolve => setTimeout(resolve, 100))]);
      }
    } else {
      activateStore.setActivated(false);
      ElMessage.error(t("app.auth.invalid", { msg: result.msg || t("app.auth.unknownError") }));
      isRouteLoading.value = true;
      await router.replace(ACTIVATE_URL);
    }
  } catch (error) {
    console.error(t("app.auth.checkFailed"));
    activateStore.setActivated(false);
    ElMessage.error(t("app.auth.checkFailed"));
    isRouteLoading.value = true;
    await router.replace(ACTIVATE_URL);
  } finally {
    isChecking.value = false;
  }
};

onBeforeMount(async () => {
  console.log("onBeforeMount start");
  await checkAuth();
  console.log("onBeforeMount end");
});

onMounted(async () => {
  unbindKeys();
  bindKeys();

  const language = globalStore.language ?? getBrowserLang();
  i18n.locale.value = language;
  globalStore.setGlobalState("language", language as LanguageType);

  // 同步语言设置到后端
  try {
    await languageApi.syncLanguage({ language });
    console.log("Initial language synced to backend:", language);
  } catch (error) {
    console.warn("Failed to sync initial language to backend:", error);
  }

  let debounceTimer: number | null = null;
  router.afterEach(() => {
    if (debounceTimer) clearTimeout(debounceTimer);
    debounceTimer = window.setTimeout(() => {
      isRouteLoading.value = false;
      debounceTimer = null;
    }, 100);
  });
});

// element language
const locale = computed(() => {
  if (globalStore.language == "zh") return zhCn;
  if (globalStore.language == "en") return en;
  if (globalStore.language == "es") return es;
  if (globalStore.language == "fr") return fr;
  return getBrowserLang() == "zh" ? zhCn : en;
});

// init theme
const { initTheme } = useTheme();
initTheme();

// element assemblySize
const assemblySize = computed(() => globalStore.assemblySize);

// element button config
const buttonConfig = reactive({ autoInsertSpace: false });
</script>

<style>
html,
body {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#app {
  width: 100%;
  height: 100%;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 全局加载样式 */
.el-loading-mask {
  background-color: rgb(255 255 255 / 90%) !important;
  backdrop-filter: blur(4px);
}
.el-loading-spinner {
  .circular {
    width: 42px;
    height: 42px;
    animation: rotate 2s linear infinite;
  }
  .el-loading-text {
    margin-top: 12px;
    font-size: 16px;
    font-weight: 500;
    color: #1a73e8;
    letter-spacing: 0.5px;
  }
  &::after {
    position: absolute;
    bottom: -20px;
    left: 50%;
    width: 200px;
    height: 3px;
    overflow: hidden;
    content: "";
    background: rgb(26 115 232 / 10%);
    border-radius: 3px;
    transform: translateX(-50%);
    &::before {
      position: absolute;
      top: 0;
      left: -50%;
      width: 50%;
      height: 100%;
      content: "";
      background: #1a73e8;
      animation: progress-bar 1.5s ease-in-out infinite;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes progress-bar {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: all 0.3s ease;
}
::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 文本选择样式 */
::selection {
  color: #1a73e8;
  background: rgb(26 115 232 / 20%);
}
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgb(255 255 255 / 90%);
  backdrop-filter: blur(4px);
}
.loading-content {
  text-align: center;
}
.loading-icon {
  margin-bottom: 16px;
}
.loading-text {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #1a73e8;
}
.loading-progress {
  width: 200px;
  margin: 0 auto;
}
.progress-bar {
  width: 100%;
  height: 6px;
  overflow: hidden;
  background: rgb(26 115 232 / 10%);
  border-radius: 6px;
  box-shadow: 0 1px 4px 0 rgb(26 115 232 / 8%);
}
.progress-inner {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--el-color-primary) 0%, var(--el-color-primary-light-2, #1a73e8) 100%);
  border-radius: 6px;
  animation: progress-bar 1.1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}
.dark .progress-inner {
  background: linear-gradient(90deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2, #34495e) 100%);
}
.loading-logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 18px;
}
.circular-loading {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 54px;
  height: 54px;
}
.circular-bg {
  width: 54px;
  height: 54px;
  transform: rotate(-90deg);
}
.circular-track {
  opacity: 0.5;
  stroke: #e0e0e0;
}
.circular-bar {
  stroke: var(--el-color-primary, #1a73e8);
  stroke-dasharray: 138;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: circular-rotate 1.2s linear infinite;
}

@keyframes circular-rotate {
  0% {
    stroke-dashoffset: 138;
  }
  100% {
    stroke-dashoffset: 0;
  }
}
.logo-svg {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  width: 32px;
  height: 28px;
  pointer-events: none;
  transform: translate(-50%, -50%);
}

/* 暗色模式适配 */
.dark .loading-container {
  background-color: rgb(30 32 40 / 92%);
}
.dark .loading-text {
  color: #41b883;
}
.dark .progress-bar {
  background: rgb(65 184 131 / 10%);
  box-shadow: 0 1px 4px 0 rgb(65 184 131 / 8%);
}
.dark .circular-track {
  stroke: #444a5a;
}
.dark .circular-bar {
  stroke: #41b883;
}
</style>
