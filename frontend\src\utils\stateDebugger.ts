import { routeStateManager } from './routeStateManager';
import { useGlobalStore } from '@/stores/modules/global';
import { useAuthStore } from '@/stores/modules/auth';

/**
 * 状态调试工具
 * 提供状态查看、保存、恢复、清除等调试功能
 */
class StateDebugger {
  private static instance: StateDebugger;

  private constructor() {
    // 在开发环境下将调试器暴露到全局
    if (import.meta.env.DEV) {
      (window as any).stateDebugger = this;
      console.log('[StateDebugger] 状态调试器已挂载到 window.stateDebugger');
    }
  }

  public static getInstance(): StateDebugger {
    if (!StateDebugger.instance) {
      StateDebugger.instance = new StateDebugger();
    }
    return StateDebugger.instance;
  }

  /**
   * 显示所有状态信息
   */
  public showAllStates(): void {
    console.group('🔍 应用状态调试信息');
    
    // 路由状态
    console.group('📍 路由状态');
    const routeState = routeStateManager.getSavedState();
    console.log('保存的路由状态:', routeState);
    console.log('当前路由:', window.location.hash);
    console.groupEnd();

    // 全局状态
    console.group('🌐 全局状态');
    const globalStore = useGlobalStore();
    console.log('全局状态:', {
      layout: globalStore.layout,
      language: globalStore.language,
      assemblySize: globalStore.assemblySize,
      maximize: globalStore.maximize,
      isDark: globalStore.isDark,
      isConsole: globalStore.isConsole,
      consoleHeight: globalStore.consoleHeight,
      isDeviceList: globalStore.isDeviceList,
      isFunctionList: globalStore.isFunctionList
    });
    console.groupEnd();

    // 认证状态
    console.group('🔐 认证状态');
    const authStore = useAuthStore();
    console.log('认证状态:', {
      menuListLength: authStore.authMenuListGet.length,
      buttonListLength: authStore.authButtonListGet.length,
      flatMenuListLength: authStore.flatMenuListGet.length
    });
    console.groupEnd();

    // 本地存储
    console.group('💾 本地存储');
    this.showLocalStorage();
    console.groupEnd();

    console.groupEnd();
  }

  /**
   * 显示本地存储信息
   */
  public showLocalStorage(): void {
    const relevantKeys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('visualdebug') || key.includes('simple-') || key.includes('language'))) {
        relevantKeys.push(key);
      }
    }

    console.log('相关的本地存储键:', relevantKeys);
    
    relevantKeys.forEach(key => {
      try {
        const value = localStorage.getItem(key);
        const parsedValue = value ? JSON.parse(value) : value;
        console.log(`${key}:`, parsedValue);
      } catch (error) {
        console.log(`${key}:`, localStorage.getItem(key));
      }
    });
  }

  /**
   * 手动保存所有状态
   */
  public saveAllStates(): void {
    console.log('🔄 手动保存所有状态...');
    
    try {
      // 保存路由状态
      routeStateManager.saveRouteState();
      console.log('✅ 路由状态已保存');

      // 全局状态会自动保存（通过Pinia持久化）
      console.log('✅ 全局状态自动保存');

      console.log('🎉 所有状态保存完成');
    } catch (error) {
      console.error('❌ 保存状态失败:', error);
    }
  }

  /**
   * 手动恢复所有状态
   */
  public restoreAllStates(): void {
    console.log('🔄 手动恢复所有状态...');
    
    try {
      // 恢复路由状态
      routeStateManager.restoreRouteState();
      console.log('✅ 路由状态已恢复');

      console.log('🎉 所有状态恢复完成');
    } catch (error) {
      console.error('❌ 恢复状态失败:', error);
    }
  }

  /**
   * 清除所有保存的状态
   */
  public clearAllStates(): void {
    const confirmed = confirm('确定要清除所有保存的状态吗？这将重置应用到初始状态。');
    if (!confirmed) return;

    console.log('🗑️ 清除所有保存的状态...');
    
    try {
      // 清除路由状态
      routeStateManager.clearSavedState();
      console.log('✅ 路由状态已清除');

      // 清除全局状态
      localStorage.removeItem('simple-global');
      console.log('✅ 全局状态已清除');

      // 清除认证状态
      localStorage.removeItem('simple-auth');
      console.log('✅ 认证状态已清除');

      // 清除其他相关状态
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('visualdebug') || key.includes('simple-'))) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log(`✅ 已清除: ${key}`);
      });

      console.log('🎉 所有状态清除完成，请刷新页面');
      
      // 询问是否刷新页面
      const shouldReload = confirm('状态已清除，是否刷新页面以应用更改？');
      if (shouldReload) {
        window.location.reload();
      }
    } catch (error) {
      console.error('❌ 清除状态失败:', error);
    }
  }

  /**
   * 模拟休眠恢复测试
   */
  public simulateSuspendResume(): void {
    console.log('🧪 模拟休眠恢复测试...');
    
    // 1. 保存当前状态
    console.log('1️⃣ 保存当前状态');
    this.saveAllStates();
    
    // 2. 等待一段时间模拟休眠
    console.log('2️⃣ 模拟休眠（等待2秒）');
    setTimeout(() => {
      console.log('3️⃣ 模拟唤醒，恢复状态');
      this.restoreAllStates();
      console.log('🎉 休眠恢复测试完成');
    }, 2000);
  }

  /**
   * 检查状态完整性
   */
  public checkStateIntegrity(): void {
    console.group('🔍 状态完整性检查');
    
    const issues = [];

    // 检查路由状态
    const routeState = routeStateManager.getSavedState();
    if (!routeState) {
      issues.push('路由状态未保存');
    } else {
      if (!routeState.currentPath) {
        issues.push('路由状态缺少路径信息');
      }
      if (!routeState.timestamp) {
        issues.push('路由状态缺少时间戳');
      }
      if (Date.now() - routeState.timestamp > 24 * 60 * 60 * 1000) {
        issues.push('路由状态已过期（超过24小时）');
      }
    }

    // 检查全局状态
    try {
      const globalStateStr = localStorage.getItem('simple-global');
      if (!globalStateStr) {
        issues.push('全局状态未保存');
      } else {
        const globalState = JSON.parse(globalStateStr);
        const requiredFields = ['layout', 'language', 'assemblySize'];
        requiredFields.forEach(field => {
          if (!globalState[field]) {
            issues.push(`全局状态缺少字段: ${field}`);
          }
        });
      }
    } catch (error) {
      issues.push('全局状态格式错误');
    }

    // 输出检查结果
    if (issues.length === 0) {
      console.log('✅ 状态完整性检查通过');
    } else {
      console.warn('⚠️ 发现以下问题:');
      issues.forEach((issue, index) => {
        console.warn(`${index + 1}. ${issue}`);
      });
    }

    console.groupEnd();
    return issues;
  }

  /**
   * 导出状态数据
   */
  public exportStates(): string {
    const stateData = {
      timestamp: new Date().toISOString(),
      routeState: routeStateManager.getSavedState(),
      localStorage: {} as Record<string, any>
    };

    // 导出相关的localStorage数据
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('visualdebug') || key.includes('simple-') || key.includes('language'))) {
        try {
          const value = localStorage.getItem(key);
          stateData.localStorage[key] = value ? JSON.parse(value) : value;
        } catch (error) {
          stateData.localStorage[key] = localStorage.getItem(key);
        }
      }
    }

    const jsonStr = JSON.stringify(stateData, null, 2);
    console.log('📤 状态数据已导出:', stateData);
    
    // 复制到剪贴板（如果支持）
    if (navigator.clipboard) {
      navigator.clipboard.writeText(jsonStr).then(() => {
        console.log('📋 状态数据已复制到剪贴板');
      }).catch(error => {
        console.warn('复制到剪贴板失败:', error);
      });
    }

    return jsonStr;
  }

  /**
   * 显示帮助信息
   */
  public help(): void {
    console.group('📖 状态调试器帮助');
    console.log('可用方法:');
    console.log('• showAllStates() - 显示所有状态信息');
    console.log('• showLocalStorage() - 显示本地存储信息');
    console.log('• saveAllStates() - 手动保存所有状态');
    console.log('• restoreAllStates() - 手动恢复所有状态');
    console.log('• clearAllStates() - 清除所有保存的状态');
    console.log('• simulateSuspendResume() - 模拟休眠恢复测试');
    console.log('• checkStateIntegrity() - 检查状态完整性');
    console.log('• exportStates() - 导出状态数据');
    console.log('• help() - 显示此帮助信息');
    console.log('');
    console.log('使用示例:');
    console.log('stateDebugger.showAllStates()');
    console.log('stateDebugger.simulateSuspendResume()');
    console.groupEnd();
  }
}

// 创建单例实例
const stateDebugger = StateDebugger.getInstance();

export { stateDebugger, StateDebugger };
export default stateDebugger;
