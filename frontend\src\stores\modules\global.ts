import { defineStore } from "pinia";
import { GlobalState, LanguageType } from "@/stores/interface";
import { DEFAULT_PRIMARY } from "@/config";
import piniaPersistConfig from "@/stores/helper/persist";
import { createMultiBackupPersist } from "@/stores/helper/enhancedPersist";

const name = "simple-global"; // 定义模块名称

// 获取存储的语言设置
const getStoredLanguage = (): LanguageType => {
  const storedLang = localStorage.getItem("language");
  return (storedLang as LanguageType) || "zh";
};

/** 全局 */
export const useGlobalStore = defineStore(name, {
  // 修改默认值之后，需清除 localStorage 数据
  state: (): GlobalState => ({
    // 布局模式 (分栏：columns | 经典：classic | 横向：transverse | 纵向：vertical)
    layout: "columns",
    // element 组件大小
    assemblySize: "default",
    // 当前系统语言
    language: getStoredLanguage(),
    // 当前页面是否全屏
    maximize: false,
    // 主题颜色
    primary: DEFAULT_PRIMARY,
    // 深色模式
    isDark: false,
    // 灰色模式
    isGrey: false,
    // 控制台
    isConsole: true,
    // 控制台高度
    consoleHeight: 154,
    // 装置列表
    isDeviceList: true,
    // 功能列表收起/展开
    isFunctionList: true,
    // 色弱模式
    isWeak: false,
    // 面包屑导航
    breadcrumb: false,
    // 面包屑导航图标
    breadcrumbIcon: false,
    // 标签页
    tabs: false,
    // 标签页图标
    tabsIcon: false,
    // 页脚
    footer: true,
    // 抽屉表单
    drawerForm: false,
    //页面水印
    watermark: true
  }),
  getters: {},
  actions: {
    // Set GlobalState
    setGlobalState(...args: ObjToKeyValArray<GlobalState>) {
      this.$patch({ [args[0]]: args[1] });
      // 如果是语言设置，同时更新 localStorage
      if (args[0] === "language") {
        localStorage.setItem("language", args[1] as string);
      }
    },
    checkColumnLayout() {
      return this.layout === "columns";
    }
  },
  persist: createMultiBackupPersist(name, [
    "layout",
    "language",
    "assemblySize",
    "maximize",
    "isDark",
    "isGrey",
    "isConsole",
    "consoleHeight",
    "isDeviceList",
    "isFunctionList",
    "isWeak",
    "breadcrumb",
    "breadcrumbIcon",
    "tabs",
    "tabsIcon",
    "footer",
    "drawerForm",
    "watermark",
    "primary"
  ])
});
