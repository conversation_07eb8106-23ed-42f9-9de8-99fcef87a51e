/**
 * English language pack
 */
export default {
  // Error messages
  errors: {
    success: "Success",
    deviceNotConnected: "Devi<PERSON> not connected",
    invalidParam: "Invalid parameter",
    operateFailed: "Operation failed",
    noData: "No data",
    internalError: "Internal error",
    connectionExists: "Connection already exists",
    fileContentEmpty: "File content is empty",
    deviceNotConnectedOrDisconnected: "Devi<PERSON> not connected or disconnected.",
    getServiceErrorInfo: "Failed to get corresponding error information",
    saveReportFileError: "Error saving report rpt file",
    getConfigureListError: "Error getting configuration list",
    loadConfigureError: "Error loading configuration",
    cancelUploadError: "Error canceling wave file upload",
    openWaveFileError: "Error opening wave file",
    getFileDataSuccess: "Successfully retrieved file data content!",
    getHistoryReportError: "Error getting history report",
    getSuccessful: "Get successful",
    errorHandledInCatch: "Error handled in catch",
    waveFileNotFound: "Wave file not found",
    waveFileSizeZero: "Wave file size is zero",
    uploadException: "Upload exception",
    uploadFinished: "Upload finished",
    saveReportXlsxError: "Error saving report xlsx file",
    invalidXmlStructure: "Invalid XML structure: missing configVersion",
    failedToGetTreeMenu: "Failed to get tree menu",
    missingHeaders: "Missing headers",
    excelParseFailed: "Excel parsing failed",
    paramModifyError: "Parameter modification error, error items are:",
    paramConfirmError: "Parameter confirmation error, reason:",
    errorReason: ", error reason:",
    invalidValue: "invalid value",
    errorItem: "Error item:",
    description: ", description",
    excelFileParseError: "Excel file parsing failed:",
    csvFileParseError: "CSV file parsing failed:",
    xmlFileParseError: "XML file parsing failed:",
    connectionFailed: "Connection failed",
    connectionTimeout: "Connection timeout",
    dataLengthMismatch: "Device returned vkeys data length does not match request length"
  },
  
  // Log messages
  logs: {
    reportController: {
      getCommonReportListEntry: "getCommonReportList input parameters",
      getCommonReportListReturn: "getCommonReportList return",
      getCommonReportListError: "getCommonReportList error",
      cancelUploadStart: "Cancel wave file upload started",
      cancelUploadError: "Cancel wave file upload error",
      openWaveFileStart: "Open wave file started",
      openWaveFileError: "Open wave file error"
    },
    configureService: {
      getConfigureListError: "Get configuration list error",
      loadConfigureError: "Load configuration error"
    },
    paramService: {
      getDiffParamComplete: "Comparison completed, number of difference groups",
      getAllDiffParamError: "getAllDiffParam error",
      getParamInfoEntry: "getParamInfo input parameters",
      getParamInfoReturn: "getParamInfo return",
      getParamInfoError: "getParamInfo error",
      startGetParamInfo: "Start getting parameter settings, pagination",
      getAllParamInfoStart: "Start getting all parameter settings",
      getAllParamInfoSuccess: "Successfully got all parameter settings, total",
      modifyParamStart: "Start modifying parameter settings",
      validateParam: "Validate parameter item",
      validateFailed: "Parameter validation failed",
      validatePassed: "Parameter validation passed, ready to send",
      setTimeout: "Set timeout",
      sendResponse: "Send response",
      modifySuccess: "Modification successful",
      sendFailed: "Send failed",
      businessError: "Business error",
      getAllDiffParamStart: "Start batch comparing parameters",
      excelParseFailed: "Excel parsing failed",
      csvParseFailed: "CSV parsing failed",
      xmlParseFailed: "XML parsing failed",
      fileParseComplete: "File parsing completed",
      getDiffParamStart: "Start single group parameter comparison",
      diffComplete: "Comparison completed, number of difference items",
      importParamStart: "Start importing parameter settings",
      paramReady: "Parameters ready to send",
      importSuccess: "Import successful",
      exportAllParamStart: "Start exporting all parameter settings",
      exportComplete: "Export completed",
      exportParamStart: "Start exporting group parameters",
      getGroupItemsStart: "Get group parameter items",
      getParamValueFailed: "Failed to get parameter values",
      getGroupItemsComplete: "Get completed, number of parameter items",
      getAllGroupItemsStart: "Get all group parameter items",
      groupParamCount: "Group: {group}, parameter items: {count}",
      getCurrentRunAreaStart: "Get current run area",
      getCurrentRunAreaSuccess: "Get successful",
      getCurrentRunAreaFailed: "Get failed",
      selectRunAreaStart: "Select setting area",
      runAreaEmpty: "Setting area cannot be empty",
      selectRunAreaSuccess: "Selection successful",
      selectRunAreaFailed: "Selection failed"
    },
    debugInfoMenuService: {
      initialized: "Initialization completed",
      getDebugInfoEntry: "getDebugInfo input parameters",
      getTreeMenuError: "getTreeMenu error",
      getTreeMenuComplete: "Processing completed, menu count"
    }
  },
  
  // Common messages
  common: {
    start: "Started",
    stop: "Stopped",
    other: "Other",
    loading: "Loading...",
    success: "Success",
    failed: "Failed",
    cancel: "Cancel",
    confirm: "Confirm",
    connectionSuccess: "Connection successful",
    requestDataSize: "Request data size",
    responseDataSize: "Response data size",
    exportMatrixSettings: "Export matrix settings"
  },

  // Dialog related
  dialogs: {
    customTitle: "Custom Title",
    customMessage: "Customize message content",
    additionalInfo: "Other additional information",
    messageBoxOpened: "Opened the message box",
    confirmButtonClicked: "click the confirm button",
    cancelButtonClicked: "click the cancel button",
    fileSelection: "File Selection",
    fileSave: "File Save",
    selectPic: "select pic",
    images: "Images",
    files: "Files",
    textFiles: "Text Files",
    all: "All"
  },

  // System folders
  systemFolders: {
    desktop: "Desktop",
    documents: "Documents",
    downloads: "Downloads",
    music: "Music",
    pictures: "Pictures",
    videos: "Videos"
  },

  // Device operations
  deviceOperations: {
    deviceIpPortExists: "Device IP port already exists",
    addCompleted: "Add completed",
    addFailed: "Add failed",
    updateCompleted: "Update completed",
    updateFailed: "Update failed"
  },

  // Configuration related
  configuration: {
    configListNotExists: "Configuration list does not exist",
    duplicateName: "Duplicate name, please re-enter",
    pathNotExists: "Path does not exist, please check if configuration is configured or saved"
  },

  // File operations
  fileOperations: {
    sourceFolderNotExists: "Source folder does not exist"
  },

  // Tray menu
  tray: {
    show: "Show",
    exit: "Exit"
  },

  // Device info
  deviceInfo: {
    basicInfo: "Device Basic Information"
  },

  // Backup related
  backup: {
    cancel: "Cancel",
    userCancel: "User cancelled backup",
    userCancelShort: "User cancelled",
    start: "Start",
    processing: "Processing",
    success: "Success",
    failed: "Failed",
    complete: "Complete",
    exception: "Exception",
    exportParam: "Export parameters",
    exportReport: "Export reports",
    exportWave: "Export wave files",
    exportConfig: "Export configuration files",
    deviceParam: "Device Parameters",
    deviceFault: "Device Fault Information",
    deviceWave: "Device Wave Files",
    deviceConfig: "Device Configuration Files",
    paramExport: "Parameter Export.xlsx",
    xmlConfig: "XML Configuration",
    logFiles: "Log Files",
    logs: "Logs",
    cancelUploadError: "Cancel upload task exception"
  },

  // Device file related
  deviceFile: {
    uploadSuccess: "Upload successful",
    cancelSuccess: "Cancel successful",
    downloadSuccess: "Download successful",
    downloadFileList: "Download File List",
    headers: {
      index: "Index",
      fileName: "Name",
      fileSize: "File Size",
      filePath: "File Path",
      lastModified: "Last Modified"
    }
  },

  // Variable related
  variable: {
    registerFailed: "Variable registration failed, reason: ",
    modifyFailed: "Variable modification failed, reason: ",
    deleteFailed: "Variable deletion failed, reason: ",
    modifyError: "Variable modification failed",
    deleteError: "Variable deletion failed",
    debugVariables: "Device Debug Variables",
    headers: {
      index: "Index",
      name: "Variable Name",
      description: "Variable Description",
      type: "Variable Type",
      value: "Variable Value"
    }
  },

  // Reports related
  reports: {
    service: "Report related Service",
    description: "Responsible for querying, exporting, clearing and other business logic of historical reports, operation reports, fault reports",
    getCommonReport: "Get common report",
    getCommonReportEntry: "Get common report method entry log",
    getGroupReport: "Get group report",
    getOperateReport: "Get operation report",
    getAuditReport: "Get audit report",
    exportCommonReport: "Export common report",
    clearReport: "Clear report",
    refreshReport: "Refresh report",
    uploadWave: "Wave file upload",
    cancelUpload: "Cancel wave file upload",
    openWaveFile: "Open wave file",
    getOperateReportEnd: "Get operation report end",
    getAuditReportEnd: "Get audit report end",
    workbookName: "report",
    exportContent: "Export content fields",
    headers: {
      reportId: "Report ID",
      reportTime: "Report Time",
      description: "Description",
      name: "Name",
      time: "Time",
      operateAddress: "Operation Address",
      operateParam: "Operation Parameter",
      value: "Value",
      step: "Step",
      source: "Source",
      sourceType: "Source Type",
      result: "Result",
      module: "Module",
      message: "Message",
      type: "Type",
      level: "Level",
      origin: "Origin",
      user: "User"
    }
  },

  // Parameters related
  params: {
    headers: {
      index: "Index",
      name: "Name",
      description: "Description",
      value: "Value",
      minValue: "Min Value",
      maxValue: "Max Value",
      step: "Step",
      unit: "Unit"
    }
  }
};
