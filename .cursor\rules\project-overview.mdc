---
description: 
globs: 
alwaysApply: false
---
# VisualDebug 项目概览

## 项目简介
VisualDebug 是一个可视化平台工程调试工具，采用多技术栈架构设计。

## 技术栈组成
- **前端**: Vue.js + TypeScript + Vite
- **桌面应用**: Electron + TypeScript
- **后端服务**: Go + FastAPI
- **构建工具**: ee-bin (自定义构建工具)

## 核心功能模块
1. **可视化工具连接** - 装置信息查看、设定量、模拟量、状态量
2. **遥信遥测遥控** - 报告、装置对时、定值导入导出、变量调试
3. **组态工具** - 预览、新增、编辑、自定义图符、关联装置信息
4. **主题定制** - IT小工具、装置配置导入导出

## 项目结构
- [frontend/](mdc:frontend) - Vue.js前端应用
- [electron/](mdc:electron) - Electron桌面应用
- [go/](mdc:go) - Go后端服务
- [python/](mdc:python) - Python FastAPI服务
- [libs/](mdc:libs) - 本地依赖库
- [build/](mdc:build) - 构建脚本
- [public/](mdc:public) - 静态资源

## 开发规范
- 遵循第一性原理进行架构设计
- 遵循DRY、KISS、SOLID、YAGNI原则
- 单个文件超过500行时需要进行模块分解
- 使用TypeScript进行类型安全开发
