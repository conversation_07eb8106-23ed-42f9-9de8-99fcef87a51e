/**
 * Electron 国际化系统
 * <AUTHOR> Assistant
 */

import zh from './locales/zh';
import en from './locales/en';
import es from './locales/es';
import fr from './locales/fr';

// 支持的语言类型
export type SupportedLanguage = 'zh' | 'en' | 'es' | 'fr';

// 语言资源
const messages = {
  zh,
  en,
  es,
  fr
};

// 当前语言设置，默认为中文
let currentLanguage: SupportedLanguage = 'zh';

/**
 * 设置当前语言
 * @param language 语言代码
 */
export function setLanguage(language: SupportedLanguage): void {
  if (messages[language]) {
    currentLanguage = language;
    console.log(`[i18n] Language set to: ${language}`);
  } else {
    console.warn(`[i18n] Unsupported language: ${language}, fallback to zh`);
    currentLanguage = 'zh';
  }
}

/**
 * 获取当前语言
 * @returns 当前语言代码
 */
export function getCurrentLanguage(): SupportedLanguage {
  return currentLanguage;
}

/**
 * 翻译函数
 * @param key 翻译键，支持点分隔的嵌套键
 * @param params 参数对象，用于替换占位符
 * @returns 翻译后的文本
 */
export function t(key: string, params?: Record<string, any>): string {
  const keys = key.split('.');
  let value: any = messages[currentLanguage];
  
  // 遍历嵌套键
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // 如果当前语言没有找到，尝试使用中文作为后备
      if (currentLanguage !== 'zh') {
        let fallbackValue: any = messages.zh;
        for (const fallbackK of keys) {
          if (fallbackValue && typeof fallbackValue === 'object' && fallbackK in fallbackValue) {
            fallbackValue = fallbackValue[fallbackK];
          } else {
            fallbackValue = null;
            break;
          }
        }
        if (fallbackValue && typeof fallbackValue === 'string') {
          value = fallbackValue;
          break;
        }
      }
      
      // 如果都没找到，返回键本身
      console.warn(`[i18n] Translation key not found: ${key} for language: ${currentLanguage}`);
      return key;
    }
  }
  
  if (typeof value !== 'string') {
    console.warn(`[i18n] Translation value is not a string for key: ${key}`);
    return key;
  }
  
  // 替换参数占位符
  if (params) {
    return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
      return params[paramKey] !== undefined ? String(params[paramKey]) : match;
    });
  }
  
  return value;
}

/**
 * 初始化国际化系统
 * @param language 初始语言
 */
export function initI18n(language: SupportedLanguage = 'zh'): void {
  setLanguage(language);
  console.log(`[i18n] Initialized with language: ${language}`);
}
