/**
 * French language pack
 */
export default {
  // Error messages
  errors: {
    success: "Succès",
    deviceNotConnected: "Appareil non connecté",
    invalidParam: "Paramètre invalide",
    operateFailed: "Opération échouée",
    noData: "Aucune donnée",
    internalError: "Erreur interne",
    connectionExists: "La connexion existe déjà",
    fileContentEmpty: "Le contenu du fichier est vide",
    deviceNotConnectedOrDisconnected: "Appareil non connecté ou déconnecté.",
    getServiceErrorInfo: "Impossible d'obtenir les informations d'erreur correspondantes",
    saveReportFileError: "Erreur lors de la sauvegarde du fichier de rapport rpt",
    getConfigureListError: "Erreur lors de l'obtention de la liste de configuration",
    loadConfigureError: "Erreur lors du chargement de la configuration",
    cancelUploadError: "Erreur lors de l'annulation du téléchargement du fichier d'onde",
    openWaveFileError: "Erreur lors de l'ouverture du fichier d'onde",
    getFileDataSuccess: "Contenu des données du fichier récupéré avec succès !"
  },
  
  // Log messages
  logs: {
    reportController: {
      getCommonReportListEntry: "getCommonReportList paramètres d'entrée",
      getCommonReportListReturn: "getCommonReportList retour",
      getCommonReportListError: "getCommonReportList erreur",
      cancelUploadStart: "Annulation du téléchargement du fichier d'onde démarrée",
      cancelUploadError: "Erreur d'annulation du téléchargement du fichier d'onde",
      openWaveFileStart: "Ouverture du fichier d'onde démarrée",
      openWaveFileError: "Erreur d'ouverture du fichier d'onde"
    },
    configureService: {
      getConfigureListError: "Erreur d'obtention de la liste de configuration",
      loadConfigureError: "Erreur de chargement de la configuration"
    },
    paramService: {
      getDiffParamComplete: "Comparaison terminée, nombre de groupes de différences",
      getAllDiffParamError: "getAllDiffParam erreur"
    }
  },
  
  // Common messages
  common: {
    start: "Démarré",
    stop: "Arrêté",
    other: "Autre",
    loading: "Chargement...",
    success: "Succès",
    failed: "Échoué",
    cancel: "Annuler",
    confirm: "Confirmer"
  },

  // Dialog related
  dialogs: {
    customTitle: "Titre personnalisé",
    customMessage: "Personnaliser le contenu du message",
    additionalInfo: "Autres informations supplémentaires",
    messageBoxOpened: "Boîte de message ouverte",
    confirmButtonClicked: "cliquer sur le bouton de confirmation",
    cancelButtonClicked: "cliquer sur le bouton d'annulation",
    fileSelection: "Sélection de fichier",
    fileSave: "Enregistrer le fichier",
    selectPic: "sélectionner une image",
    images: "Images",
    files: "Fichiers",
    textFiles: "Fichiers texte",
    all: "Tout"
  },

  // System folders
  systemFolders: {
    desktop: "Bureau",
    documents: "Documents",
    downloads: "Téléchargements",
    music: "Musique",
    pictures: "Images",
    videos: "Vidéos"
  },

  // Device operations
  deviceOperations: {
    deviceIpPortExists: "Le port IP du périphérique existe déjà",
    addCompleted: "Ajout terminé",
    addFailed: "Échec de l'ajout",
    updateCompleted: "Mise à jour terminée",
    updateFailed: "Échec de la mise à jour"
  },

  // Configuration related
  configuration: {
    configListNotExists: "La liste de configuration n'existe pas",
    duplicateName: "Nom en double, veuillez ressaisir",
    pathNotExists: "Le chemin n'existe pas, veuillez vérifier si la configuration est configurée ou enregistrée"
  },

  // File operations
  fileOperations: {
    sourceFolderNotExists: "Le dossier source n'existe pas"
  }
};
