import {windowService} from "../../service/os/window";

/**
 * window窗口相关
 * @class
 */
class WindowController {
  /**
   * closeWindow
   * 关闭窗口
   */
  async closeWindow(type) {
    await windowService.closeWindow(type);
  }

  /**
   * maximizeWindow
   * 最大化窗口
   */
  async maximizeWindow() {
    await windowService.maximizeWindow();
  }

  /**
   * minimizeWindow
   * 最小化窗口
   */
  async minimizeWindow() {
    await windowService.minimizeWindow();
  }

  async printScreen() {
    await windowService.printScreen();
  }

  async openDevTools() {
    await windowService.openDevTools();
  }

  /**
   * dragWindow
   * 拖拽窗口
   */
  async dragWindow() {
    await windowService.dragWindow();
  }

  /**
   * saveWindowState
   * 保存窗口状态
   */
  async saveWindowState() {
    await windowService.saveWindowState();
  }

  /**
   * restoreWindowState
   * 恢复窗口状态
   */
  async restoreWindowState() {
    await windowService.restoreWindowState();
  }
}

WindowController.toString = () => '[class WindowController]';

export default WindowController;