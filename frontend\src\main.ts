import { createApp } from "vue";
import App from "./App.vue";

// 性能监控
import { markAppMounted, markResourcesLoaded } from "@/utils/performance";
// 启动缓存
import { warmupStartupCache } from "@/utils/startupCache";
// 启动配置
import { startupConfig, applyStartupOptimizations } from "@/config/startup";

// 关键CSS样式 - 立即加载
import "@/styles/reset.scss";
import "@/styles/common.scss";
import "element-plus/dist/index.css";
import "virtual:uno.css";

// 核心依赖 - 立即加载
import ElementPlus from "element-plus";
import router from "@/routers";
import pinia from "@/stores";
import errorHandler from "@/utils/errorHandler";
import I18n from "@/languages/index";
import { routeStateManager } from "@/utils/routeStateManager";

// 启动测试（仅开发模式）
if (import.meta.env.DEV) {
  import("@/utils/startupTest").catch(console.warn);
}

// 创建应用实例
const app = createApp(App);

// 应用启动优化配置
applyStartupOptimizations(startupConfig);

// 错误处理
app.config.errorHandler = errorHandler;

// 注册核心插件
app
  .use(ElementPlus, {
    size: "default",
    zIndex: 3000
  })
  .use(router)
  .use(pinia)
  .use(I18n);

// 预热缓存（异步执行，不阻塞应用挂载）
warmupStartupCache();

// 立即挂载应用，提高启动速度
app.mount("#app");

// 标记应用挂载完成
markAppMounted();

// 初始化路由状态管理器
routeStateManager.initialize(router);

// 延迟加载非关键资源和功能
setTimeout(async () => {
  try {
    // 延迟加载样式文件
    await Promise.all([
      import("@/assets/iconfont/iconfont.scss").catch(console.warn),
      import("@/assets/iconfontPlus/iconfont.scss").catch(console.warn),
      import("@/assets/fonts/font.scss").catch(console.warn),
      import("element-plus/theme-chalk/dark/css-vars.css").catch(console.warn),
      import("@/styles/element-dark.scss").catch(console.warn),
      import("@/styles/element.scss").catch(console.warn),
      import("highlight.js/styles/atom-one-dark.css").catch(console.warn),
      import("v-contextmenu/dist/themes/default.css").catch(console.warn)
    ]);

    // 延迟加载和注册组件
    const [
      directivesModule,
      BLRowModule,
      BLColModule,
      iconifyModule,
      IconsModule,
      hljsVuePluginModule,
      contextmenuModule,
      hljsCommonModule,
      echartsModule
    ] = await Promise.all([
      import("@/directives/index").catch(() => ({ default: null })),
      import("@/components/Common/BLRow.vue").catch(() => ({ default: null })),
      import("@/components/Common/BLCol.vue").catch(() => ({ default: null })),
      import("@/utils/iconify").catch(() => ({
        downloadAndInstall: () => console.warn("图标加载失败")
      })),
      import("@element-plus/icons-vue").catch(() => ({})),
      import("@highlightjs/vue-plugin").catch(() => ({ default: null })),
      import("v-contextmenu").catch(() => ({ default: null })),
      import("highlight.js/lib/common").catch(() => ({ default: null })),
      import("echarts/core").catch(() => ({
        use: () => console.warn("ECharts加载失败")
      }))
    ]);

    const directives = directivesModule.default;
    const BLRow = BLRowModule.default;
    const BLCol = BLColModule.default;
    const { downloadAndInstall } = iconifyModule;

    // 注册延迟加载的插件
    if (directives) {
      app.use(directives);
    }
    if (hljsVuePluginModule.default) {
      app.use(hljsVuePluginModule.default);
    }
    if (contextmenuModule.default) {
      app.use(contextmenuModule.default);
    }

    // 注册全局组件
    if (BLRow && BLCol) {
      app.component("BlRow", BLRow).component("BLCol", BLCol);
    }

    // 按需注册常用图标
    const iconList = [
      "Edit",
      "Delete",
      "Search",
      "Refresh",
      "Plus",
      "Minus",
      "ColdDrink",
      "Setting",
      "Notification",
      "CircleCheckFilled",
      "ChromeFilled",
      "CircleClose",
      "FolderDelete",
      "Remove",
      "DArrowLeft",
      "DArrowRight",
      "More"
    ];

    iconList.forEach(key => {
      if (IconsModule[key]) {
        app.component(key, IconsModule[key]);
      }
    });

    // 异步加载图标集合
    downloadAndInstall();

    // 延迟初始化 ECharts
    const { LineChart, BarChart, PieChart } = await import("echarts/charts");
    const { TitleComponent, TooltipComponent, LegendComponent, GridComponent } = await import("echarts/components");
    const { CanvasRenderer } = await import("echarts/renderers");

    echartsModule.use([LineChart, BarChart, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, CanvasRenderer]);

    // 延迟初始化 highlight.js
    try {
      if (hljsCommonModule.default && hljsCommonModule.default.highlightAuto) {
        hljsCommonModule.default.highlightAuto("<h1>Highlight.js has been registered successfully!</h1>").value;
      }
    } catch (error) {
      console.warn("Highlight.js初始化失败:", error);
    }

    // 标记资源加载完成
    markResourcesLoaded();
  } catch (error) {
    console.warn("延迟资源加载失败:", error);
    markResourcesLoaded(); // 即使失败也要标记完成
  }
}, 50); // 50ms后开始加载非关键资源
