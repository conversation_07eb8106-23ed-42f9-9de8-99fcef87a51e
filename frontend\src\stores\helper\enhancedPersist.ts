import { PersistedStateOptions } from "pinia-plugin-persistedstate";

/**
 * 增强的持久化配置
 * 提供更强的数据持久化能力，防止休眠后状态丢失
 */

interface EnhancedPersistOptions {
  key: string;
  paths?: string[];
  storage?: Storage;
  beforeRestore?: (context: any) => void;
  afterRestore?: (context: any) => void;
  serializer?: {
    serialize: (value: any) => string;
    deserialize: (value: string) => any;
  };
  debug?: boolean;
}

/**
 * 创建增强的持久化配置
 */
const createEnhancedPersist = (options: EnhancedPersistOptions): PersistedStateOptions => {
  const {
    key,
    paths,
    storage = localStorage,
    beforeRestore,
    afterRestore,
    serializer,
    debug = false
  } = options;

  return {
    key,
    storage,
    paths,
    serializer: serializer || {
      serialize: (value: any) => {
        try {
          const serialized = JSON.stringify(value, null, debug ? 2 : 0);
          if (debug) {
            console.log(`[EnhancedPersist] 序列化 ${key}:`, value);
          }
          return serialized;
        } catch (error) {
          console.error(`[EnhancedPersist] 序列化失败 ${key}:`, error);
          return '{}';
        }
      },
      deserialize: (value: string) => {
        try {
          const deserialized = JSON.parse(value);
          if (debug) {
            console.log(`[EnhancedPersist] 反序列化 ${key}:`, deserialized);
          }
          return deserialized;
        } catch (error) {
          console.error(`[EnhancedPersist] 反序列化失败 ${key}:`, error);
          return {};
        }
      }
    },
    beforeRestore: (context) => {
      if (debug) {
        console.log(`[EnhancedPersist] 开始恢复 ${key}`);
      }
      beforeRestore?.(context);
    },
    afterRestore: (context) => {
      if (debug) {
        console.log(`[EnhancedPersist] 恢复完成 ${key}`);
      }
      afterRestore?.(context);
      
      // 添加额外的验证和修复逻辑
      validateAndFixState(context.store, key, debug);
    }
  };
};

/**
 * 验证和修复状态
 */
const validateAndFixState = (store: any, key: string, debug: boolean) => {
  try {
    // 这里可以添加特定的状态验证和修复逻辑
    if (key === 'simple-global') {
      validateGlobalState(store, debug);
    } else if (key === 'simple-auth') {
      validateAuthState(store, debug);
    }
  } catch (error) {
    console.error(`[EnhancedPersist] 状态验证失败 ${key}:`, error);
  }
};

/**
 * 验证全局状态
 */
const validateGlobalState = (store: any, debug: boolean) => {
  const requiredFields = ['layout', 'language', 'assemblySize'];
  const defaultValues = {
    layout: 'columns',
    language: 'zh',
    assemblySize: 'default',
    maximize: false,
    isDark: false,
    isGrey: false,
    isConsole: true,
    consoleHeight: 154,
    isDeviceList: true,
    isFunctionList: true,
    isWeak: false,
    breadcrumb: false,
    breadcrumbIcon: false,
    tabs: false,
    tabsIcon: false,
    footer: true,
    drawerForm: false,
    watermark: true
  };

  let hasChanges = false;
  
  for (const [field, defaultValue] of Object.entries(defaultValues)) {
    if (store[field] === undefined || store[field] === null) {
      store[field] = defaultValue;
      hasChanges = true;
      if (debug) {
        console.log(`[EnhancedPersist] 修复全局状态字段 ${field}:`, defaultValue);
      }
    }
  }

  if (hasChanges && debug) {
    console.log('[EnhancedPersist] 全局状态已修复');
  }
};

/**
 * 验证认证状态
 */
const validateAuthState = (store: any, debug: boolean) => {
  // 确保认证状态的基本字段存在
  if (!store.authMenuList) {
    store.authMenuList = [];
  }
  if (!store.authButtonList) {
    store.authButtonList = [];
  }
  
  if (debug) {
    console.log('[EnhancedPersist] 认证状态已验证');
  }
};

/**
 * 多重备份存储策略
 */
class MultiBackupStorage {
  private primaryStorage: Storage;
  private backupStorage: Storage;
  private key: string;

  constructor(key: string, primaryStorage: Storage = localStorage, backupStorage: Storage = sessionStorage) {
    this.key = key;
    this.primaryStorage = primaryStorage;
    this.backupStorage = backupStorage;
  }

  getItem(key: string): string | null {
    try {
      // 首先尝试从主存储获取
      let value = this.primaryStorage.getItem(key);
      if (value) {
        // 同时更新备份存储
        this.backupStorage.setItem(key, value);
        return value;
      }

      // 如果主存储没有，尝试从备份存储获取
      value = this.backupStorage.getItem(key);
      if (value) {
        // 恢复到主存储
        this.primaryStorage.setItem(key, value);
        console.log(`[MultiBackupStorage] 从备份存储恢复数据: ${key}`);
        return value;
      }

      return null;
    } catch (error) {
      console.error(`[MultiBackupStorage] 获取数据失败: ${key}`, error);
      return null;
    }
  }

  setItem(key: string, value: string): void {
    try {
      // 同时保存到主存储和备份存储
      this.primaryStorage.setItem(key, value);
      this.backupStorage.setItem(key, value);
    } catch (error) {
      console.error(`[MultiBackupStorage] 保存数据失败: ${key}`, error);
      // 如果主存储失败，至少尝试保存到备份存储
      try {
        this.backupStorage.setItem(key, value);
      } catch (backupError) {
        console.error(`[MultiBackupStorage] 备份存储也失败: ${key}`, backupError);
      }
    }
  }

  removeItem(key: string): void {
    try {
      this.primaryStorage.removeItem(key);
      this.backupStorage.removeItem(key);
    } catch (error) {
      console.error(`[MultiBackupStorage] 删除数据失败: ${key}`, error);
    }
  }

  clear(): void {
    try {
      this.primaryStorage.clear();
      this.backupStorage.clear();
    } catch (error) {
      console.error('[MultiBackupStorage] 清空存储失败', error);
    }
  }

  get length(): number {
    try {
      return this.primaryStorage.length;
    } catch (error) {
      console.error('[MultiBackupStorage] 获取存储长度失败', error);
      return 0;
    }
  }

  key(index: number): string | null {
    try {
      return this.primaryStorage.key(index);
    } catch (error) {
      console.error('[MultiBackupStorage] 获取存储键失败', error);
      return null;
    }
  }
}

/**
 * 创建多重备份的持久化配置
 */
const createMultiBackupPersist = (key: string, paths?: string[]): PersistedStateOptions => {
  const multiStorage = new MultiBackupStorage(key);
  
  return createEnhancedPersist({
    key,
    paths,
    storage: multiStorage as Storage,
    debug: process.env.NODE_ENV === 'development',
    beforeRestore: () => {
      console.log(`[MultiBackupPersist] 开始恢复多重备份数据: ${key}`);
    },
    afterRestore: () => {
      console.log(`[MultiBackupPersist] 多重备份数据恢复完成: ${key}`);
    }
  });
};

export {
  createEnhancedPersist,
  createMultiBackupPersist,
  MultiBackupStorage
};

export default createEnhancedPersist;
