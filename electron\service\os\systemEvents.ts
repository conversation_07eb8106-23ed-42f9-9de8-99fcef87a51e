import { app, powerMonitor, BrowserWindow } from 'electron';
import { getMainWindow } from 'ee-core/electron';
import { logger } from 'ee-core/log';
import { windowStateService } from './windowState';

/**
 * 系统事件监听服务
 * 监听各种系统事件，确保应用状态的正确保存和恢复
 */
class SystemEventsService {
  private isInitialized = false;
  private suspendTimer: NodeJS.Timeout | null = null;
  private resumeTimer: NodeJS.Timeout | null = null;

  /**
   * 初始化系统事件监听
   */
  public initialize(): void {
    if (this.isInitialized) return;

    logger.info('[SystemEventsService] 初始化系统事件监听');

    // 等待应用准备就绪
    if (app.isReady()) {
      this.setupEventListeners();
    } else {
      app.whenReady().then(() => {
        this.setupEventListeners();
      });
    }

    this.isInitialized = true;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    try {
      // 监听系统休眠事件
      powerMonitor.on('suspend', this.handleSuspend.bind(this));

      // 监听系统唤醒事件
      powerMonitor.on('resume', this.handleResume.bind(this));

      // 监听锁屏事件
      powerMonitor.on('lock-screen', this.handleLockScreen.bind(this));

      // 监听解锁事件
      powerMonitor.on('unlock-screen', this.handleUnlockScreen.bind(this));

      // 监听系统关机事件
      powerMonitor.on('shutdown', this.handleShutdown.bind(this));

      // 监听应用即将退出事件
      app.on('before-quit', this.handleBeforeQuit.bind(this));

      // 监听所有窗口关闭事件
      app.on('window-all-closed', this.handleWindowAllClosed.bind(this));

      // 监听应用激活事件（macOS）
      app.on('activate', this.handleActivate.bind(this));

      // 监听应用失去焦点事件
      app.on('browser-window-blur', this.handleWindowBlur.bind(this));

      // 监听应用获得焦点事件
      app.on('browser-window-focus', this.handleWindowFocus.bind(this));

      // 监听窗口最小化事件
      app.on('browser-window-minimize', this.handleWindowMinimize.bind(this));

      // 监听窗口恢复事件
      app.on('browser-window-restore', this.handleWindowRestore.bind(this));

      logger.info('[SystemEventsService] 系统事件监听器已设置');
    } catch (error) {
      logger.error('[SystemEventsService] 设置事件监听器失败:', error);
    }
  }

  /**
   * 处理系统休眠事件
   */
  private handleSuspend(): void {
    logger.info('[SystemEventsService] 系统即将休眠');
    
    // 清除之前的定时器
    if (this.suspendTimer) {
      clearTimeout(this.suspendTimer);
    }

    // 立即保存状态
    this.saveApplicationState();

    // 设置一个短暂的延迟，确保状态保存完成
    this.suspendTimer = setTimeout(() => {
      logger.info('[SystemEventsService] 休眠前状态保存完成');
    }, 100);
  }

  /**
   * 处理系统唤醒事件
   */
  private handleResume(): void {
    logger.info('[SystemEventsService] 系统已唤醒');

    // 清除休眠定时器
    if (this.suspendTimer) {
      clearTimeout(this.suspendTimer);
      this.suspendTimer = null;
    }

    // 延迟恢复状态，确保系统完全唤醒
    this.resumeTimer = setTimeout(() => {
      this.restoreApplicationState();
      logger.info('[SystemEventsService] 唤醒后状态恢复完成');
    }, 1000);
  }

  /**
   * 处理锁屏事件
   */
  private handleLockScreen(): void {
    logger.info('[SystemEventsService] 屏幕已锁定');
    this.saveApplicationState();
  }

  /**
   * 处理解锁事件
   */
  private handleUnlockScreen(): void {
    logger.info('[SystemEventsService] 屏幕已解锁');
    
    // 短暂延迟后恢复状态
    setTimeout(() => {
      this.restoreApplicationState();
    }, 500);
  }

  /**
   * 处理系统关机事件
   */
  private handleShutdown(): void {
    logger.info('[SystemEventsService] 系统即将关机');
    this.saveApplicationState();
  }

  /**
   * 处理应用即将退出事件
   */
  private handleBeforeQuit(): void {
    logger.info('[SystemEventsService] 应用即将退出');
    this.saveApplicationState();
  }

  /**
   * 处理所有窗口关闭事件
   */
  private handleWindowAllClosed(): void {
    logger.info('[SystemEventsService] 所有窗口已关闭');
    this.saveApplicationState();
  }

  /**
   * 处理应用激活事件
   */
  private handleActivate(): void {
    logger.info('[SystemEventsService] 应用已激活');
    
    // 如果没有窗口，创建一个新窗口
    const mainWindow = getMainWindow();
    if (!mainWindow || mainWindow.isDestroyed()) {
      // 这里可以触发创建新窗口的逻辑
      logger.info('[SystemEventsService] 主窗口不存在，可能需要重新创建');
    } else {
      // 恢复窗口状态
      this.restoreApplicationState();
    }
  }

  /**
   * 处理窗口失去焦点事件
   */
  private handleWindowBlur(event: any, window: BrowserWindow): void {
    logger.debug('[SystemEventsService] 窗口失去焦点');
    // 可以在这里添加一些逻辑，比如定时保存状态
  }

  /**
   * 处理窗口获得焦点事件
   */
  private handleWindowFocus(event: any, window: BrowserWindow): void {
    logger.debug('[SystemEventsService] 窗口获得焦点');
    // 可以在这里检查是否需要恢复状态
  }

  /**
   * 处理窗口最小化事件
   */
  private handleWindowMinimize(event: any, window: BrowserWindow): void {
    logger.debug('[SystemEventsService] 窗口已最小化');
    this.saveApplicationState();
  }

  /**
   * 处理窗口恢复事件
   */
  private handleWindowRestore(event: any, window: BrowserWindow): void {
    logger.debug('[SystemEventsService] 窗口已恢复');
    this.restoreApplicationState();
  }

  /**
   * 保存应用状态
   */
  private saveApplicationState(): void {
    try {
      // 保存窗口状态
      windowStateService.saveWindowState();
      
      // 这里可以添加保存其他应用状态的逻辑
      // 比如当前打开的文件、用户设置等
      
      logger.debug('[SystemEventsService] 应用状态已保存');
    } catch (error) {
      logger.error('[SystemEventsService] 保存应用状态失败:', error);
    }
  }

  /**
   * 恢复应用状态
   */
  private restoreApplicationState(): void {
    try {
      // 恢复窗口状态
      windowStateService.restoreWindowState();
      
      // 这里可以添加恢复其他应用状态的逻辑
      
      logger.debug('[SystemEventsService] 应用状态已恢复');
    } catch (error) {
      logger.error('[SystemEventsService] 恢复应用状态失败:', error);
    }
  }

  /**
   * 获取系统电源状态信息
   */
  public getPowerInfo(): any {
    try {
      return {
        onBatteryPower: powerMonitor.isOnBatteryPower(),
        systemIdleTime: powerMonitor.getSystemIdleTime(),
        systemIdleState: powerMonitor.getSystemIdleState(60), // 60秒阈值
      };
    } catch (error) {
      logger.error('[SystemEventsService] 获取电源信息失败:', error);
      return null;
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    if (this.suspendTimer) {
      clearTimeout(this.suspendTimer);
      this.suspendTimer = null;
    }

    if (this.resumeTimer) {
      clearTimeout(this.resumeTimer);
      this.resumeTimer = null;
    }

    logger.info('[SystemEventsService] 系统事件服务已清理');
  }
}

// 创建单例实例
const systemEventsService = new SystemEventsService();

export { systemEventsService, SystemEventsService };
